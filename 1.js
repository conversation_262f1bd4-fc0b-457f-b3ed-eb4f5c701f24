window.showInteractiveFaultSetPanel = function () {
  // HTML 结构保持不变，使用标准的 placeholder option
  var panelHtml = `
        <div class="layui-form" style="padding: 20px;">
            <!-- 区域一：生成结果 (置顶) -->
            <div class="layui-form-item layui-form-text">
                <textarea id="fault-set-result" placeholder="点击“生成”按钮后，结果将显示在此处" class="layui-textarea" readonly style="height: 250px; font-family: 'Consolas', 'Monaco', 'monospace'; background-color: #f8f8f8;"></textarea>
            </div>

            <!-- 区域二：参数与操作 (一行显示) -->
            <div class="layui-form-item">
                <!-- 电压等级 -->
                <div class="layui-inline">
                    <select id="voltage-level-select" lay-filter="voltageLevel">
                        <option value="" disabled selected hidden>电压等级</option>
                    </select>
                </div>
                <!-- 故障类型 -->
                <div class="layui-inline">
                    <select id="fault-type-select">
                        <option value="单永N-1故障" selected>单永N-1故障</option>
                        <option value="三永N-1故障">三永N-1故障</option>
                        <option value="三永N-2故障">三永N-2故障</option>
                    </select>
                </div>
                <!-- 线路搜索 -->
                <div class="layui-inline" style="position: relative; width: 300px;">
                    <input type="text" id="line-search-input" placeholder="线路搜索" autocomplete="off" class="layui-input">
                    <div id="line-search-results" class="layui-anim layui-anim-upbit" style="display: none; position: absolute; top: 40px; left: 0; width: 100%; max-height: 150px; overflow-y: auto; background-color: #fff; border: 1px solid #ddd; z-index: 1000; box-shadow: 0 2px 4px rgba(0,0,0,.12);"></div>
                </div>
                <!-- 生成按钮 -->
                <div class="layui-inline">
                    <button class="layui-btn" id="generate-btn">生成</button>
                </div>
            </div>

            <!-- 区域三：文件操作 (一行显示) -->
            <div class="layui-form-item">
                <!-- 文件选择 -->
                <div class="layui-inline">
                    <button type="button" class="layui-btn layui-btn-primary" id="file-select-btn">文件选择</button>
                </div>
                <!-- 时间选择器 -->
                <div class="layui-inline">
                    <input type="text" id="datetime-picker" placeholder="选择时间" class="layui-input" style="width: 180px;">
                </div>
                <!-- 文件名称 -->
                <div class="layui-inline">
                    <input type="text" id="filename-input" placeholder="文件名称" class="layui-input" style="width: 200px;">
                </div>
            </div>

            <!-- 区域四：计算按钮 (独立一行) -->
            <div class="layui-form-item" style="text-align: center;">
                <button class="layui-btn layui-btn-normal" id="calculate-btn">计算</button>
            </div>
        </div>
    `;

  // 打开 Layui 弹窗
  layer.open({
    type: 1,
    title: "交互式故障集生成",
    area: ["900px", "650px"],
    content: panelHtml,
    success: function (layero, index) {
      console.log("🔔==", yw_zlMap);
      var form = layui.form;
      var laydate = layui.laydate;
      var allLines = Object.values(yw_zlMap || {});
      var selectedLine = null;

      var $voltageSelect = $("#voltage-level-select");
      var $faultTypeSelect = $("#fault-type-select");
      var $searchInput = $("#line-search-input");
      var $searchResults = $("#line-search-results");
      var $generateBtn = $("#generate-btn");
      var $resultTextarea = $("#fault-set-result");

      // 新增元素
      var $fileSelectBtn = $("#file-select-btn");
      var $datetimePicker = $("#datetime-picker");
      var $filenameInput = $("#filename-input");
      var $calculateBtn = $("#calculate-btn");

      // --- 初始化电压等级的函数 (已简化) ---
      function initVoltageLevels() {
        var voltageLevels = new Set();
        allLines.forEach((line) => {
          if (line.vltype && line.vltype.trim() !== "") {
            voltageLevels.add(line.vltype);
          }
        });

        var sortedLevels = Array.from(voltageLevels)
          .map(Number)
          .sort((a, b) => a - b);

        sortedLevels.forEach((level) => {
          $voltageSelect.append(`<option value="${level}">${level}kv</option>`);
        });

        form.render("select"); // 重新渲染 select
      }

      // --- 其他所有函数保持不变 ---
      function performSearch() {
        selectedLine = null;
        var keyword = $searchInput.val().trim().toLowerCase();
        var voltage = $voltageSelect.val();
        if (keyword.length < 1) {
          $searchResults.hide();
          return;
        }
        var filteredLines = allLines.filter((line) => {
          var voltageMatch = !voltage || line.vltype === voltage;
          var keywordMatch =
            line.name.toLowerCase().includes(keyword) ||
            line.start.toLowerCase().includes(keyword) ||
            line.end.toLowerCase().includes(keyword);
          return voltageMatch && keywordMatch;
        });
        renderSearchResults(filteredLines.slice(0, 10));
      }

      function renderSearchResults(lines) {
        if (lines.length === 0) {
          $searchResults
            .html('<p style="padding: 10px; color: #999;">无匹配结果</p>')
            .show();
          return;
        }
        var html = lines
          .map(
            (line) =>
              `<div class="search-result-item" data-id="${line.id}" style="padding: 8px 15px; cursor: pointer;">
                        ${line.name} (${line.start} - ${line.end})
                    </div>`
          )
          .join("");
        $searchResults.html(html).show();
      }

      function selectLine(lineId) {
        selectedLine = yw_zlMap[lineId];
        if (selectedLine) {
          $searchInput.val(selectedLine.name);
        }
        $searchResults.hide();
      }

      function generateFaultSet() {
        if (!selectedLine) {
          layer.msg("请先搜索并选择一条线路！", { icon: 5 });
          return;
        }
        var faultType = $faultTypeSelect.val();
        var requestData = {
          faultType: faultType,
          lines: [
            {
              id: selectedLine.id, // 直接传递完整ID，让后端解析
            },
          ],
        };
        var loadingIndex = layer.load(2);
        $.ajax({
          url: commonUrl.baseUrl + "faultSet/generate",
          type: "POST",
          contentType: "application/json",
          data: JSON.stringify(requestData),
          success: function (res) {
            layer.close(loadingIndex);
            if (res && res.code === 0 && res.data && res.data.faultSetText) {
              var currentText = $resultTextarea.val();
              var newText = res.data.faultSetText;
              $resultTextarea.val(
                currentText + (currentText ? "\n" : "") + newText
              );
              layer.msg("生成成功！", { icon: 1 });
            } else {
              layer.alert("生成失败: " + (res.msg || "未知错误"), { icon: 2 });
            }
          },
          error: function () {
            layer.close(loadingIndex);
            layer.alert("请求后端服务失败，请检查网络或联系管理员。", {
              icon: 2,
            });
          },
        });
      }

      // --- 新增功能函数 ---

      // 生成文件名
      function generateFileName(datetime) {
        if (!datetime) return "";
        var date = new Date(datetime);
        var year = date.getFullYear();
        var month = String(date.getMonth() + 1).padStart(2, "0");
        var day = String(date.getDate()).padStart(2, "0");
        var hour = String(date.getHours()).padStart(2, "0");
        var minute = String(date.getMinutes()).padStart(2, "0");
        var second = String(date.getSeconds()).padStart(2, "0");
        return `BPA${year}${month}${day}${hour}${minute}${second}.swi`;
      }

      // 初始化时间选择器
      function initDatetimePicker() {
        var now = new Date();
        var defaultTime =
          now.getFullYear() +
          "-" +
          String(now.getMonth() + 1).padStart(2, "0") +
          "-" +
          String(now.getDate()).padStart(2, "0") +
          " " +
          String(now.getHours()).padStart(2, "0") +
          ":" +
          String(now.getMinutes()).padStart(2, "0") +
          ":" +
          String(now.getSeconds()).padStart(2, "0");

        $datetimePicker.val(defaultTime);
        $filenameInput.val(generateFileName(defaultTime));

        laydate.render({
          elem: "#datetime-picker",
          type: "datetime",
          format: "yyyy-MM-dd HH:mm:ss",
          value: defaultTime,
          done: function (value) {
            $filenameInput.val(generateFileName(value));
          },
        });
      }

      // 文件选择处理 - 触发时间选择器
      function handleFileSelect() {
        // 触发时间选择器的点击事件
        $datetimePicker.click();
      }

      // 计算按钮处理
      function handleCalculate() {
        var resultText = $resultTextarea.val().trim();
        if (!resultText) {
          layer.msg("请先生成故障集数据！", { icon: 5 });
          return;
        }

        console.log("=== 故障集计算结果 ===");
        console.log(resultText);
        console.log("=== 计算完成 ===");

        layer.msg("计算完成，请查看控制台输出", { icon: 1 });
      }

      // --- 事件绑定 ---
      $searchInput.on("keyup", performSearch);
      $searchResults.on("click", ".search-result-item", function () {
        var lineId = $(this).data("id");
        selectLine(lineId);
      });
      $(document).on("click", function (e) {
        if (
          !$(e.target).closest("#line-search-input, #line-search-results")
            .length
        ) {
          $searchResults.hide();
        }
      });
      $generateBtn.on("click", generateFaultSet);

      // 新增事件绑定
      $fileSelectBtn.on("click", handleFileSelect);
      $calculateBtn.on("click", handleCalculate);

      // --- 初始调用 ---
      initVoltageLevels();
      initDatetimePicker();
    },
  });
};
